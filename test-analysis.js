#!/usr/bin/env node

/**
 * Test script to verify AI analysis functionality
 */

async function testBenjiAnalysis() {
  console.log('🧪 Testing Benji AI Analysis...\n');
  
  // Import the analysis function
  const { getBenjiForUser } = await import('./apps/web/src/lib/benji-agent');
  
  // Test tweet content
  const testTweet = "Just built an amazing AI-powered app with @vercel and @supabase! The future is here and it's incredible! 🚀 #AI #BuildInPublic";
  
  const testAuthor = {
    name: "Tech Entrepreneur",
    handle: "techbuilder",
    followers: 5000,
    verified: false
  };
  
  try {
    console.log('📝 Test Tweet:', testTweet);
    console.log('👤 Test Author:', testAuthor);
    console.log('\n🤖 Starting AI analysis...');
    
    // Create Benji instance for test user
    const benji = await getBenjiForUser('test-user-id');
    
    // Perform full analysis
    const analysis = await benji.performFullAnalysis(testTweet, testAuthor);
    
    console.log('\n✅ AI Analysis Results:');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    console.log(`🎯 Bullish Score: ${analysis.bullishScore}/100`);
    console.log(`⭐ Importance Score: ${analysis.importanceScore}/100`);
    console.log(`🏷️  Keywords: [${analysis.keywords.join(', ')}]`);
    console.log('\n📊 Analysis Data:');
    console.log(`   Sentiment: ${analysis.analysisData.sentiment}`);
    console.log(`   Priority: ${analysis.analysisData.priority}`);
    console.log(`   Recommended Action: ${analysis.analysisData.recommendedAction}`);
    console.log(`   Confidence: ${(analysis.analysisData.confidence * 100).toFixed(1)}%`);
    console.log(`   Processing Time: ${analysis.analysisData.processingTime}ms`);
    
    console.log('\n🎉 Test completed successfully!');
    return true;
    
  } catch (error) {
    console.error('\n❌ Test failed:', error);
    return false;
  }
}

// Run the test
testBenjiAnalysis().then(success => {
  process.exit(success ? 0 : 1);
});