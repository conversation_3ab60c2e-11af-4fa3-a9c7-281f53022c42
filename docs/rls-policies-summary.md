# Row Level Security (RLS) Policies Summary

## Overview
This document outlines all the Row Level Security policies implemented for the BuddyChip application's Supabase database.

## Security Model
- **Regular Users**: Can only access their own data
- **Admin Users**: Can access and modify all data (identified by `isAdmin = true`)
- **Public Access**: Limited to reading active/public records only

## Tables with RLS Enabled

### 1. users
**RLS Status**: ✅ Enabled

**Policies**:
- `users_select_own_policy` (SELECT): Users can read their own profile
- `users_insert_own_policy` (INSERT): Users can create their own profile (for auto-creation)
- `users_update_own_policy` (UPDATE): Users can update their own profile
- `users_select_admin_policy` (SELECT): <PERSON><PERSON> can read all users
- `users_update_admin_policy` (UPDATE): Ad<PERSON> can update any user
- `users_delete_admin_policy` (DELETE): Ad<PERSON> can delete users

### 2. personality_profiles
**RLS Status**: ✅ Enabled

**Policies**:
- `personality_profiles_select_policy` (SELECT): Everyone can read active personality profiles
- `personality_profiles_insert_policy` (INSERT): Only admins can create personality profiles
- `personality_profiles_update_policy` (UPDATE): Only admins can update personality profiles
- `personality_profiles_delete_policy` (DELETE): Only admins can delete personality profiles

### 3. subscription_plans
**RLS Status**: ✅ Enabled

**Policies**:
- `subscription_plans_select_policy` (SELECT): Everyone can read active subscription plans
- `subscription_plans_insert_policy` (INSERT): Only admins can create subscription plans
- `subscription_plans_update_policy` (UPDATE): Only admins can update subscription plans
- `subscription_plans_delete_policy` (DELETE): Only admins can delete subscription plans

### 4. plan_features
**RLS Status**: ✅ Enabled

**Policies**:
- `plan_features_select_policy` (SELECT): Everyone can read features for active subscription plans
- `plan_features_insert_policy` (INSERT): Only admins can create plan features
- `plan_features_update_policy` (UPDATE): Only admins can update plan features
- `plan_features_delete_policy` (DELETE): Only admins can delete plan features

### 5. ai_models
**RLS Status**: ✅ Enabled

**Policies**:
- `ai_models_select_policy` (SELECT): Everyone can read active AI models
- `ai_models_insert_policy` (INSERT): Only admins can create AI models
- `ai_models_update_policy` (UPDATE): Only admins can update AI models
- `ai_models_delete_policy` (DELETE): Only admins can delete AI models

## Security Features

### Admin Detection
All admin-only policies use this pattern to verify admin status:
```sql
EXISTS (
    SELECT 1 FROM "users" 
    WHERE "users"."id" = auth.uid()::text 
    AND "users"."isAdmin" = true
)
```

### Active Record Filtering
Public read access is limited to active records using:
```sql
"isActive" = true
```

### User Ownership
User-specific access uses:
```sql
"id" = auth.uid()::text
```

## Benefits
1. **Data Isolation**: Users can only access their own data
2. **Admin Control**: Admins have full access for management
3. **Public Safety**: Only active/approved content is publicly readable
4. **Automatic Enforcement**: Security is enforced at the database level
5. **Performance**: Policies are optimized with proper indexing

## Testing RLS Policies
To test these policies:
1. Create test users with different roles (regular user, admin)
2. Attempt CRUD operations on each table
3. Verify that access is properly restricted based on user role
4. Test with authenticated and unauthenticated requests

## Maintenance
- Review policies when adding new tables
- Update admin detection logic if user roles change
- Monitor performance impact of complex policies
- Regular security audits of policy effectiveness
